"use client";

import Link from "next/link";
import { Button } from "./ui/button";
import { usePathname, useRouter } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Menu, Globe, PenTool } from "lucide-react";
import {
  She<PERSON>,
  <PERSON>et<PERSON>ontent,
  Sheet<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { signIn, signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  header: {
    logo: string;
    nav: {
      features: string;
      pricing: string;
      faq: string;
    };
    cta: {
      login: string;
      signup: string;
    };
    userMenu: {
      myOrders: string;
      signOut: string;
      profile: string;
    };
  };
}

export default function Header({ header }: HeaderProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = pathname.split('/')[1];
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const switchLocale = (locale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${locale}`);
    window.location.href = newPathname;
  };

  const handleGetStarted = () => {
    router.push(`/${currentLocale}/app/login`);
  };

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Left: Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
              <PenTool className="w-6 h-6 text-white" />
            </div>
            <Link href={`/${currentLocale}`} className="text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
              {header.logo}
            </Link>
          </div>

          {/* Center: Navigation Links - Desktop Only */}
          <div className="hidden md:flex items-center space-x-8">
            {Object.entries(header.nav).map(([key, value]) => (
              <a
                key={key}
                href={`#${key}`}
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                {value}
              </a>
            ))}

            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                <Globe className="h-4 w-4" />
                <span>{localeNames[currentLocale]}</span>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {locales.map((locale) => (
                  <DropdownMenuItem
                    key={locale}
                    onClick={() => switchLocale(locale)}
                    className="cursor-pointer"
                  >
                    {localeNames[locale]}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Login/User Menu */}
            {session ? (
              <div className="relative" ref={dropdownRef}>
                <button
                  type="button"
                  className="flex items-center rounded-full bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  <span className="sr-only">Open user menu</span>
                  {session.user?.image ? (
                    <Image
                      className="h-8 w-8 rounded-full"
                      src={session.user.image}
                      alt={session.user.name || ''}
                      width={32}
                      height={32}
                      unoptimized
                      priority
                    />
                  ) : (
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-600">
                        {session.user?.name?.charAt(0) || '?'}
                      </span>
                    </div>
                  )}
                </button>

                {isDropdownOpen && (
                  <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                    <div className="px-4 py-2 text-sm text-gray-700">
                      <div className="font-medium">{session.user?.name}</div>
                      <div className="text-gray-500">{session.user?.email}</div>
                    </div>
                    <div className="border-t border-gray-100" />
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      {header.userMenu.profile}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Button
                onClick={handleGetStarted}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {header.cta.signup}
              </Button>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden">
            <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle className="text-left flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <PenTool className="w-4 h-4 text-white" />
                  </div>
                  <span>{header.logo}</span>
                </SheetTitle>
              </SheetHeader>
              <div className="flex flex-col space-y-4 mt-6">
                {Object.entries(header.nav).map(([key, value]) => (
                  <a
                    key={key}
                    href={`#${key}`}
                    className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors"
                  >
                    {value}
                  </a>
                ))}

                {/* Language Switcher */}
                <div className="border-t border-gray-200 pt-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Language</h3>
                  {locales.map((locale) => (
                    <button
                      key={locale}
                      onClick={() => switchLocale(locale)}
                      className={`block w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                        currentLocale === locale
                          ? "bg-blue-50 text-blue-600 font-medium"
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {localeNames[locale]}
                    </button>
                  ))}
                </div>

                {session ? (
                  <div className="flex flex-col space-y-3 pt-4 border-t border-gray-200">
                    <div className="font-medium">{session.user?.name}</div>
                    <div className="text-gray-500">{session.user?.email}</div>
                    <Link
                      href={`/${currentLocale}/orders`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                    >
                      {header.userMenu.myOrders}
                    </Link>
                    <Link
                      href={`/${currentLocale}/profile`}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                    >
                      {header.userMenu.profile}
                    </Link>
                    <button
                      type="button"
                      onClick={() => signOut({ callbackUrl: `/${currentLocale}` })}
                      className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                    >
                      {header.userMenu.signOut}
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-3 pt-4 border-t border-gray-200">
                    <Button
                      onClick={() => signIn()}
                      variant="outline"
                      className="w-full"
                    >
                      {header.cta.login}
                    </Button>
                    <Button
                      onClick={handleGetStarted}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {header.cta.signup}
                    </Button>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}